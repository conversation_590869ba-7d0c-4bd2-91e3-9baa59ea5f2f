import { API_TAGS } from "@/lib/constants";
import { createRouter, json<PERSON>ontent, validator } from "@/lib/helpers";
import { openApiResponses, responses } from "@/lib/responses";
import { createPaginatedSchema } from "@/lib/schemas";
import { authGuard } from "@/middlewares/auth.middleware";
import { permissionGuard } from "@/middlewares/permission.middleware";
import { describeRoute } from "hono-openapi";
import * as HttpStatusCodes from "stoker/http-status-codes";
import { z } from "zod";
import {
  JobWithAuthorDTO,
  PublicJobDTO,
  citiesByCountryDTO,
  createJobDTO,
  listJobsQueryDTO,
  listJobsWithAuthorDTO,
  rawJobResponseDTO,
  updateJobDTO,
} from "./dtos";
import { JobVacancyOperations } from "./operations";

const router = createRouter()
  .get(
    "/",
    describeRoute({
      title: "List Job Vacancies",
      description:
        "List all job vacancies. Public view shows limited data, admin/editor view shows full data with author info.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          createPaginatedSchema(z.union([PublicJobDTO, JobWithAuthorDTO])),
          "List of job vacancies",
        ),
      },
    }),
    validator("query", listJobsQueryDTO.optional()),
    async (c) => {
      const query = c.req.valid("query");
      const user = c.var.user;
      const isAdminOrEditor =
        !!user && (user.role === "admin" || user.role === "editor");
      const vacancies = await JobVacancyOperations.findMany(
        query,
        isAdminOrEditor,
      );
      return c.json(vacancies, HttpStatusCodes.OK);
    },
  )
  .get(
    "/own",
    authGuard,
    describeRoute({
      title: "List Own Job Vacancies",
      description: "List all job vacancies created by the current user.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          listJobsWithAuthorDTO,
          "List of user's job vacancies with author info",
        ),
        ...openApiResponses.unauthorized,
      },
    }),
    validator(
      "query",
      listJobsQueryDTO
        .extend({
          status: z.enum(["published", "archived", "draft"]).optional(),
        })
        .optional(),
    ),
    async (c) => {
      const query = c.req.valid("query");
      const userId = c.var.checkedUser.id;
      const vacancies = await JobVacancyOperations.findByAuthor(userId, query);
      return c.json(vacancies, HttpStatusCodes.OK);
    },
  )
  .get(
    "/locations",
    describeRoute({
      title: "Get Cities by Country",
      description:
        "Get cities grouped by countries from all published job vacancies. Returns only the allowed country codes (BE, FR, AT, UA, NL, DE) that have published vacancies.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          citiesByCountryDTO,
          "Cities grouped by countries",
        ),
      },
    }),
    async (c) => {
      const citiesByCountry = await JobVacancyOperations.getCitiesByCountry();
      return c.json(citiesByCountry, HttpStatusCodes.OK);
    },
  )
  .get(
    "/:id",
    describeRoute({
      title: "Get Job Vacancy",
      description:
        "Get a specific job vacancy by ID or slug. Publicly available.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          z.union([PublicJobDTO, JobWithAuthorDTO]),
          "Single job vacancy",
        ),
        ...openApiResponses.notFound,
      },
    }),
    async (c) => {
      const { id } = c.req.param();
      const user = c.var.user;
      const isAdminOrEditor =
        !!user && (user.role === "admin" || user.role === "editor");

      let vacancy = await JobVacancyOperations.findById(
        id,
        isAdminOrEditor,
        user?.id,
      );
      if (!vacancy) {
        vacancy = await JobVacancyOperations.findBySlug(
          id,
          isAdminOrEditor,
          user?.id,
        );
      }

      if (!vacancy) {
        return responses.notFound(c, `Job vacancy with ID/slug ${id}`);
      }

      return c.json(vacancy, HttpStatusCodes.OK);
    },
  )
  .post(
    "/",
    authGuard,
    describeRoute({
      title: "Create Job Vacancy",
      description: "Create a new job vacancy. Requires 'create' permission.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.CREATED]: jsonContent(
          rawJobResponseDTO,
          "Created job vacancy",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
      },
    }),
    permissionGuard({ jobVacancy: ["create"] }),
    validator("json", createJobDTO.omit({ authorId: true, editorId: true })),
    async (c) => {
      const data = c.req.valid("json");
      const vacancy = await JobVacancyOperations.create({
        ...data,
        authorId: c.var.checkedUser.id,
      });

      // if (vacancy.status === "published") {
      //   await TelegramBotService.sendVacancyMessage(vacancy.id).catch(
      //     (error) => {
      //       console.error(error);
      //     },
      //   );
      // }

      return c.json(vacancy, HttpStatusCodes.CREATED);
    },
  )
  .patch(
    "/:id",
    authGuard,
    describeRoute({
      title: "Update Job Vacancy",
      description:
        "Update an existing job vacancy by ID. Requires 'update' permission. Note: This endpoint only accepts the internal ID, not the slug.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.OK]: jsonContent(
          rawJobResponseDTO,
          "Updated job vacancy",
        ),
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ jobVacancy: ["update"] }),
    validator("json", updateJobDTO),
    async (c) => {
      const { id } = c.req.param();
      const user = c.var.user;
      const isAdminOrEditor =
        !!user && (user.role === "admin" || user.role === "editor");
      const updates = c.req.valid("json");
      const vacancy = await JobVacancyOperations.update(
        id,
        updates,
        user?.id,
        isAdminOrEditor,
      );

      if (!vacancy) {
        return responses.notFound(c, `Job vacancy with ID ${id}`);
      }
      // TODO fix redis
      // await TelegramBotService.updateVacancyMessage({ id: vacancy.id }).catch(
      //   (error) => {
      //     console.error(error);
      //   },
      // );
      return c.json(vacancy, HttpStatusCodes.OK);
    },
  )
  .delete(
    "/:id",
    authGuard,
    describeRoute({
      title: "Delete Job Vacancy",
      description:
        "Delete a job vacancy by ID. Requires 'delete' permission or editor role. Authors can only delete their own vacancies. Note: This endpoint only accepts the internal ID, not the slug.",
      tags: [API_TAGS.JOB_VACANCIES],
      responses: {
        [HttpStatusCodes.NO_CONTENT]: { description: "Job vacancy deleted" },
        ...openApiResponses.unauthorized,
        ...openApiResponses.forbidden,
        ...openApiResponses.notFound,
      },
    }),
    permissionGuard({ jobVacancy: ["delete"] }),
    // TODO archive instead of delete and when user is deleted anonymize user and archive job vacancy
    async (c) => {
      const { id } = c.req.param();
      const userId = c.var.checkedUser.id;
      const userRole = c.var.checkedUser.role;

      const vacancy = await JobVacancyOperations.findRawById(id);
      if (!vacancy) {
        return responses.notFound(c, `Job vacancy with ID ${id}`);
      }

      if (userRole === "author" && vacancy.authorId !== userId) {
        return responses.forbidden(c);
      }

      const deleted = await JobVacancyOperations.delete(id);
      if (!deleted) {
        return responses.notFound(c, `Job vacancy with ID ${id}`);
      }

      return c.body(null, HttpStatusCodes.NO_CONTENT);
    },
  );

export default router;
